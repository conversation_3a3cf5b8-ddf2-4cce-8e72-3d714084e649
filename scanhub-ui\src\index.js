import React from 'react';
import ReactDOM from 'react-dom/client';
// Bootstrap CSS
import "bootstrap/dist/css/bootstrap.min.css";
// main css file CSS
import './index.css';
// Bootstrap Bundle JS
import "bootstrap/dist/js/bootstrap.bundle.min";
import App from './App';
import reportWebVitals from './reportWebVitals';
import axios from 'axios';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
    <App />
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals

// Create an Axios instance
const apiClient = axios.create({
  baseURL: process.env. REACT_APP_API_URL // Replace with your API base URL
});

// Add a request interceptor
apiClient.interceptors.request.use(
  async (config) => {
    
      try {
        const token = JSON.parse(localStorage.getItem('account.token'));
        config.headers.Authorization =`${process.env.REACT_APP_BEARER_TOKEN} `+ token;
      } catch (error) {
          console.error('Token acquisition error:', error);
      }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export default apiClient;

reportWebVitals();
