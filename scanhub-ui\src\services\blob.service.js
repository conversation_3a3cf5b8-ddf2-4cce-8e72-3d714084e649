import apiClient from '../index';

export const uploadIncidentFile = (request) => {
    return apiClient.post(`/api/Blob/UploadIncidentFile`,request,{ headers: { 'Content-Type': 'multipart/form-data'}});
};

export const deleteDocumnet = (request) => {
    return apiClient.post(`/api/Blob/DeleteDocumnet`,request,{ headers: { 'Content-Type': 'multipart/form-data'}});
};

export const downloadFile = (request) => {
    return apiClient.post(`/api/DownloadBlob/DownloadFile`,request);
};

export const viewFile = (request) => {
    return apiClient.post(`/api/DownloadBlob/ViewFile`,request);
};

export const base64ToArrayBuffer = (base64) => {
    const binaryString = window.atob(base64);
    const binaryLen = binaryString.length;
    const bytes = new Uint8Array(binaryLen);
    for (let i = 0; i < binaryLen; i++) {
        const ascii = binaryString.charCodeAt(i);
        bytes[i] = ascii;
    }
    return bytes;
};