import React, { useState, useEffect, useMemo } from 'react';
import Header from './Header';
import IdleLogout from './IdleLogout';
import GlobalContext from './GlobalContext';
import { useNavigate } from 'react-router-dom';
import { getUserAccess } from './services/authentication.service';
import { logout } from './services/authentication.service';

const Layout = (props) => {
    const [keys, setKeys] = useState(() => {
        const savedState = localStorage.getItem('account.auth');
        return savedState ? JSON.parse(savedState) : {};
    });
    const [isLoading, setIsLoading] = useState(false);
    const [userAccess, setUserAccess] = useState(() => {
        const savedState = JSON.parse(localStorage.getItem('account.access'));
        if (savedState) {
            // savedState.auto_category_id= dataDec(keys, savedState.auto_category_id);
            // savedState.auto_incident_id= dataDec(keys, savedState.auto_incident_id);
            // savedState.auto_report_id= dataDec(keys, savedState.auto_report_id);
            // savedState.is_access= dataDec(keys,savedState.is_access);
            return savedState;
        }
        else {
            return {};
        }
    });
    let navigate = useNavigate();
    useEffect(() => {
        //fetchUserAccess();
    }, []);

    // fetch user access
    const fetchUserAccess = async (keyx, s_id) => {
        try {
            await getUserAccess(s_id).then((resp) => {
                localStorage.setItem('account.access', JSON.stringify(resp.data));
                // let is_submit_report=dataDec(keyx, resp.data.auto_incident_id);
                // let is_reports_analytics= dataDec(keyx, resp.data.auto_report_id)
                setUserAccess({
                    // auto_category_id: dataDec(keyx, resp.data.auto_category_id),
                    // auto_incident_id: is_submit_report,
                    // auto_report_id: is_reports_analytics,
                    // is_access:dataDec(keyx,resp.data.is_access)
                });
                navigate('/facilities');
            }).catch((error) => {
                const status = error.response ? error.response.status : null;
                if (status === 403) { navigate('/forbidden'); }
                if (status === 401) { alert('session expired'); logout(); navigate('/'); }
                if (status === 400) {
                    alert('Unauthorized User!');
                }
                console.log('Failed to fetch data: ' + error);
                GlobalContext.stopSpinner();
            })
        } catch (er) {
            console.error('Error fetching items shafi: ', er);
        }
    };
    const startSpinner = async () => {
        setIsLoading(true);
    };
    const stopSpinner = async () => {
        setIsLoading(false);
    };
    return (
        <GlobalContext.Provider value={useMemo(() => ({
            keys: keys,
            userAccess: userAccess,
            startSpinner,
            stopSpinner
        }))}>
            <React.Fragment>
            {isLoading&&<div className="loader-wrapper"><div className="loader"></div></div>}
                <main className="d-flex justify-content-center px-0">
                    <div className="row h-100 w-100 mt-4">
                        <Header />
                        {props.children}
                    </div>
                    <IdleLogout/>
                </main>
            </React.Fragment>
        </GlobalContext.Provider>
    );
};
export default Layout;