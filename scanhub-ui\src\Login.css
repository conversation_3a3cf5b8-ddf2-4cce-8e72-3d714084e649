/* Login page specific body styles */
.login-page-body {
    min-height: 100vh;
    min-height: -webkit-fill-available; /* iOS Safari fix */
    align-items: center;
    background-attachment: scroll; /* Fixed attachment causes issues on iOS Safari */
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    display: flex;
    justify-content: center;
    overflow: auto; /* Allow scrolling on mobile */
    padding: 1rem 0; /* Add vertical padding for mobile */
}

/* Safari-specific fixes */
@supports (-webkit-touch-callout: none) {
    .login-page-body {
        min-height: -webkit-fill-available;
        background-attachment: scroll;
    }
}

.login-container {
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    width: 100%;
    max-width: 500px; /* Ensure container doesn't get too wide */
    margin: 0 auto; /* Center the container */
}

.login-header {
    text-align: center;
    /* position: fixed; */
    top: 7rem;
    left: 0;
    right: 0;
    z-index: 1;

}

.login-body {
    padding: 2rem;
}

.login-header .logo-scan, .login-header .logo-hub {
    font-size: 1.5rem !important;
    font-weight: 600;
}


.login-header .logo-hub {
    color: #fff;
    padding: 0 8px;
    border-radius: 4px;
}

.password-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #6c757d;
}

.btn-continue {
    color: #fff;
    font-weight: 500;
    padding: 10px;
}

.btn-continue:hover {
    background-color: #005f85;
    color: #d0d7dd;
}

.login-header .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Georgia, 'Times New Roman', Times, serif;
    font-size: 2rem;
    font-weight: 400;
    color: #000;
    text-align: center;
    margin-bottom: 0.5rem;
}

.login-header .logo img {
    width: 2rem;
    height: 2rem;
    max-width: none;
    margin-right: 10px;
    margin-bottom: 0;
    display: inline-block;
    vertical-align: middle;
}

.form-control {
    background-color: #f9f9f9;
    font-size: 16px; /* Prevent zoom on iOS Safari */
    -webkit-appearance: none; /* Remove iOS Safari default styling */
    border-radius: 8px; /* Consistent border radius */
    padding: 12px 16px; /* Better touch targets for mobile */
    border: 1px solid #ddd;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    border-color: #005f85;
    box-shadow: 0 0 0 0.2rem rgba(0, 95, 133, 0.25);
    outline: none;
}

/* iOS Safari specific input fixes */
@supports (-webkit-touch-callout: none) {
    .form-control {
        font-size: 16px; /* Prevents zoom on focus */
        transform: translateZ(0); /* Force hardware acceleration */
    }
}

@media (max-width: 1444px) {
    .login-header{
        top: 0.5rem;
    }
}

/* iPad and smaller tablets */
@media (max-width: 768px) {
    .login-page-body {
        padding: 0.5rem;
    }

    .info {
        font-size: 1.25rem;
    }

    .sub-info {
        font-size: 1rem;
    }

    .login-header {
        padding: 1rem;
        top: 0.5rem;
    }

    .login-body {
        padding: 1.5rem;
    }

    .login-container {
        margin: 0.5rem;
        border-radius: 12px;
    }
}

/* Mobile phones */
@media (max-width: 576px) {
    .login-page-body {
        padding: 0.25rem;
        min-height: 100vh;
        min-height: -webkit-fill-available; /* iOS Safari fix */
    }

    .login-header {
        padding: 1rem 0.5rem; /* Reduced excessive padding */
        top: 0;
    }

    .login-body {
        padding: 1rem;
    }

    .logo {
        font-size: 1.5rem;
    }

    .login-container {
        margin: 0.25rem;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .form-control {
        padding: 14px 16px; /* Larger touch targets */
        font-size: 16px; /* Prevent zoom */
    }

    .btn-continue {
        padding: 14px 16px;
        font-size: 16px;
        font-weight: 600;
    }

    .login-header .logo {
        margin-bottom: 1.5rem;
    }
    .login-header .logo img {
        width: 1.5rem;
        height: auto;
        margin-bottom: 0.1rem;
    }
}

/* iPhone specific fixes */
@media only screen
  and (device-width: 375px)
  and (device-height: 667px)
  and (-webkit-device-pixel-ratio: 2),
only screen
  and (device-width: 375px)
  and (device-height: 812px)
  and (-webkit-device-pixel-ratio: 3),
only screen
  and (device-width: 414px)
  and (device-height: 896px)
  and (-webkit-device-pixel-ratio: 2),
only screen
  and (device-width: 414px)
  and (device-height: 896px)
  and (-webkit-device-pixel-ratio: 3) {

    .login-page-body {
        min-height: 100vh;
        min-height: -webkit-fill-available;
        padding: 0.5rem 0.25rem;
    }

    .login-header {
        padding: 0.5rem;
        margin-bottom: 1rem;
    }

    .login-container {
        width: calc(100% - 0.5rem);
        margin: 0 auto;
    }

    .form-control {
        -webkit-appearance: none;
        border-radius: 8px;
        font-size: 16px;
        padding: 12px 16px;
    }

    .input-group.position-relative {
        position: relative;
        display: flex;
        align-items: center;
    }

    .password-toggle {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
        pointer-events: auto;
    }

    .login-header .logo {
        margin-bottom: 2rem;
    }
}

/* iOS Safari landscape orientation fix */
@media screen and (max-height: 500px) and (orientation: landscape) {
    .login-page-body {
        min-height: 100vh;
        padding: 0.25rem;
        align-items: flex-start;
        justify-content: center;
    }

    .login-header {
        padding: 0.25rem;
        margin-bottom: 0.5rem;
    }

    .login-body {
        padding: 0.75rem;
    }

    .logo {
        font-size: 1.25rem;
    }
}