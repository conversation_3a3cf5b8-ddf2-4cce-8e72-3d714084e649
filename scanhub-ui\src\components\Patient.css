.patient-details {
    flex-grow: 1;
    overflow-y: auto;
    padding: 1rem 0.5rem;
    height: calc(100vh - 230px);
    background-color: #f9f9f9;
}

.card-title {
    font-size: 1rem;
    font-weight: 400;
}

.action-icons {
    display: flex;
    gap: 0.5rem;
}

.cursor-pointer {
    cursor: pointer;
}

/* code-scan <PERSON><PERSON> Styles */
.code-scan-btn {
    position: fixed;
    bottom: 20px;
    right: 0;
    left: 0;
    z-index: 1000;
    text-align: center;
}

.code-scan-btn .btn {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    align-items: center;
    justify-content: center;
}

@media (max-width: 576px) {
    .card.medication-card {
        border-left: none;
        border-right: none;
        border-bottom: none;
        border-radius: unset;
        background-color: #f9f9f9;
    }

    .code-scan-btn .btn {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}