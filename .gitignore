# dependencies
/node_modules

# production build
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# editor directories and files
.idea
.vscode
*.sublime-project
*.sublime-workspace

# Mac system files
*.DS_Store

# Optional npm cache directory
.npm

# Parcel-bundler cache
.cache

# Coverage directory used by testing tools
coverage

# TypeScript cache
*.tsbuildinfo
