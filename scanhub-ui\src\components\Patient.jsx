import { React, useEffect, useState, useContext, useRef } from "react";
import { useNavigate, useLocation } from 'react-router-dom';
import './Patient.css';
import globalContext from '../GlobalContext';
import { getPatientDetails } from '../services/patient.services';
import { getProductDetails } from '../services/product.services';
import moment from 'moment';
import { logout } from '../services/authentication.service';
import { faQrcode, faEdit, faTrashAlt } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Html5Qrcode, Html5QrcodeSupportedFormats } from 'html5-qrcode';

const Patient = () => {
    const location = useLocation();
    let navigate = useNavigate();
    const GlobalContext = useContext(globalContext);
    const [facilityObj, setFacilityObj] = useState({});
    const [selectedUnits, setSelectedUnits] = useState([]);
    const [patient, setPatient] = useState({});
    const [scannedCode, setScannedCode] = useState('');
    const [error, setError] = useState('');
    const [scannerRunning, setScannerRunning] = useState(false);
    const scannerRef = useRef(null);
    const [isMyFaciliitesTabActive, setIsMyFaciliitesTabActive] = useState(true);
    const [isPinndedTabActive, setIsPinndedTabActive] = useState(true);
    const [isSorted, setIsSorted] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPageNo, setCurrentPageNo] = useState(1);

    useEffect(() => {
        setFacilityObj(location.state.facilityObj);
        setSelectedUnits(location.state.selectedUnits);
        setIsMyFaciliitesTabActive(location.state.isMyFaciliitesTabActive);
        setIsPinndedTabActive(location.state.isPinndedTabActive);
        setCurrentPageNo(location.state.currentPageNo);
        setSearchTerm(location.state.searchTerm);
        setCurrentPageNo(location.state.currentPageNo);
        setIsSorted(location.state.isSorted);
        fetchData();
    }, []);

    const fetchData = async () => {
        try {
            GlobalContext.startSpinner();
            let request = {
                Param1: String(location.state.facilityObj.Facility_ID),
                Param2: String(location.state.patientObj.AccountNumber)
            };
            await getPatientDetails(request).then((resp) => {
                setPatient(resp.data ? resp.data.Patient : {});
                GlobalContext.stopSpinner();
            }).catch((error) => {
                const status = error.response ? error.response.status : null;
                if (status === 403) { navigate('/forbidden'); }
                if (status === 401) { alert('session expired'); logout(); navigate('/'); }
                console.log('Failed to fetch data: ' + error);
                GlobalContext.stopSpinner();
            });
        } catch (er) {
            console.error('Error fetching items: ', er);
        }
    };

    const backToPatient = () => {
        navigate('/patients', {
            state: { facilityObj: facilityObj,isMyFaciliitesTabActive:isMyFaciliitesTabActive, selectedUnits: selectedUnits,currentPageNo:currentPageNo,searchTerm:searchTerm,isSorted:isSorted  }
        });
    }

    const fetchProductDetails = async (code) => {
        try {
            // let request = {
            //     Param1: String(code)
            // };
            // const response = await getProductDetails(request);
            // const data = await response.json();
            // console.log('Product Details:', data);
            fetchData();
        } catch (err) {
            console.error('Failed to fetch product details:', err);
        }
    };

    const startScanner = () => {
        if (scannerRunning) return;

        const html5QrCode = new Html5Qrcode("reader");
        scannerRef.current = html5QrCode;

        const config = {
            fps: 10,
            qrbox: 250,
            formatsToSupport: [
                Html5QrcodeSupportedFormats.QR_CODE,
                Html5QrcodeSupportedFormats.CODE_128,
                Html5QrcodeSupportedFormats.EAN_13,
                Html5QrcodeSupportedFormats.UPC_A,
                Html5QrcodeSupportedFormats.UPC_E,
                Html5QrcodeSupportedFormats.CODE_39,
                Html5QrcodeSupportedFormats.CODE_93,
                Html5QrcodeSupportedFormats.ITF
            ]
        };

        html5QrCode
            .start(
                { facingMode: "environment" },
                config,
                async (decodedText) => {
                    setScannedCode(decodedText);
                    alert(`Scanned: ${decodedText}`);
                    await fetchProductDetails(decodedText);

                    html5QrCode.stop().then(() => {
                        console.log("Scanner stopped after successful scan");
                        setScannerRunning(false);
                    }).catch((err) => console.error("Failed to stop scanner", err));
                },
                (errorMessage) => {
                    console.warn("Scan error:", errorMessage);
                }
            )
            .then(() => {
                setScannerRunning(true);
                console.log("Scanner started");
            })
            .catch((err) => {
                console.error("Error starting scanner", err);
                setError("Failed to start scanner");
            });
    };

    const stopScanner = () => {
        if (!scannerRunning || !scannerRef.current) {
            console.warn("Scanner not running");
            return;
        }

        scannerRef.current
            .stop()
            .then(() => {
                console.log("Scanner stopped");
                setScannerRunning(false);
            })
            .catch((err) => {
                console.error("Error stopping scanner", err);
            });
    };



    return (
        <div className="col-lg-12 desktop-container p-0 d-flex flex-column">
            <a className="back-btn-link p-2 border-bottom fw-bold custom-color-maroon btn-link cursor-pointer" onClick={backToPatient}>Patients</a>
            <div className="text-left p-2 border-bottom fw-bold fs-5">Patient Details</div>
            <div className="patient-details patient-info p-3 custom-bg-light-gray">
                <h5 className="mb-2">{patient.PatientName} | <small className="text-muted fw-normal">{moment(patient.Dob).format('MM/DD/YY')} ({patient.Sex})</small>
                </h5>

                <div className="row mb-2 bg-white rounded-3 shadow-sm m-auto p-3 border-1">
                    <div className="col-6">
                        <div className="d-flex align-items-center mb-2">
                            <span>{patient.AccountNumber}</span>
                        </div>
                        <div className="d-flex align-items-center mb-2">
                            <span>{moment(patient.AdmissionDate).format('MM/DD/YY')}</span>
                        </div>
                    </div>
                    <div className="col-6">
                        <div className="d-flex align-items-center mb-2">
                            <span>{patient.PatientLocation}</span>
                        </div>
                    </div>
                    <div className="col-12">
                        <div className="d-flex align-items-center mb-2">
                            <span>Acute pancreatitis without necrosis or infection</span>
                        </div>
                    </div>
                </div>
                <div className="p-md-3 flex-grow-1 custom-bg-light-gray">
                    <div className="row g-3">
                        <div className="col-12">
                            <div className="mb-4">
                                <h6 className="section-title mb-3">
                                    Administered
                                </h6>

                                <h6 className="mt-4 mb-3">
                                    MEDICATIONS
                                </h6>

                                <div className="row g-sm-3">
                                    <div className="col-md-6 col-lg-4 p-0 px-md-2">
                                        <div className="card medication-card h-100">
                                            <div className="card-body">
                                                <div className="d-flex justify-content-between align-items-start mb-2">
                                                    <h5 className="card-title mb-0">IV Sodium Chloride 0.9% 1000ml</h5>
                                                    <span className="badge bg-light text-secondary">Qty: 5</span>
                                                </div>
                                                <div className="d-flex justify-content-between align-items-center">
                                                    <div className="text-muted">
                                                        09/22/24 12:35 PM
                                                    </div>
                                                    <div className="action-icons">
                                                        <FontAwesomeIcon icon={faEdit} className="text-warning me-2 cursor-pointer" />
                                                        <FontAwesomeIcon icon={faTrashAlt} className="text-danger cursor-pointer" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="col-md-6 col-lg-4 p-0 px-md-2">
                                        <div className="card medication-card h-100">
                                            <div className="card-body">
                                                <div className="d-flex justify-content-between align-items-start mb-2">
                                                    <h5 className="card-title mb-0">Nioride (Zofran) 4mg</h5>
                                                    <span className="badge bg-light text-secondary">Qty: 3</span>
                                                </div>
                                                <div className="d-flex justify-content-between align-items-center">
                                                    <div className="text-muted">
                                                        Administered
                                                    </div>
                                                    <div className="action-icons">
                                                        <FontAwesomeIcon icon={faEdit} className="text-warning me-2 cursor-pointer" />
                                                        <FontAwesomeIcon icon={faTrashAlt} className="text-danger cursor-pointer" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="col-md-6 col-lg-4 p-0 px-md-2">
                                        <div className="card medication-card h-100">
                                            <div className="card-body">
                                                <div className="d-flex justify-content-between align-items-start mb-2">
                                                    <h5 className="card-title mb-0">IV Morphine Sulphate 2mg</h5>
                                                    <span className="badge bg-light text-secondary">Qty: 5</span>
                                                </div>
                                                <div className="d-flex justify-content-between align-items-center">
                                                    <div className="text-muted">
                                                        09/22/24 12:35 PM
                                                    </div>
                                                    <div className="action-icons">
                                                        <FontAwesomeIcon icon={faEdit} className="text-warning me-2 cursor-pointer" />
                                                        <FontAwesomeIcon icon={faTrashAlt} className="text-danger cursor-pointer" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="col-md-6 col-lg-4 p-0 px-md-2">
                                        <div className="card medication-card h-100">
                                            <div className="card-body">
                                                <div className="d-flex justify-content-between align-items-start mb-2">
                                                    <h5 className="card-title mb-0">Lactated Ringers 1000ml/hr</h5>
                                                    <span className="badge bg-light text-secondary">Qty: 5</span>
                                                </div>
                                                <div className="d-flex justify-content-between align-items-center">
                                                    <div className="text-muted">
                                                        09/22/24 12:35 PM
                                                    </div>
                                                    <div className="action-icons">
                                                        <FontAwesomeIcon icon={faEdit} className="text-warning me-2 cursor-pointer" />
                                                        <FontAwesomeIcon icon={faTrashAlt} className="text-danger cursor-pointer" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="col-md-6 col-lg-4 p-0 px-md-2">
                                        <div className="card medication-card h-100">
                                            <div className="card-body">
                                                <div className="d-flex justify-content-between align-items-start mb-2">
                                                    <h5 className="card-title mb-0">IV Protoxin 40mg</h5>
                                                    <span className="badge bg-light text-secondary">Qty: 5</span>
                                                </div>
                                                <div className="d-flex justify-content-between align-items-center">
                                                    <div className="text-muted">
                                                        09/22/24 12:35 PM
                                                    </div>
                                                    <div className="action-icons">
                                                        <FontAwesomeIcon icon={faEdit} className="text-warning me-2 cursor-pointer" />
                                                        <FontAwesomeIcon icon={faTrashAlt} className="text-danger cursor-pointer" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="col-md-6 col-lg-4 p-0 px-md-2">
                                        <div className="card medication-card h-100">
                                            <div className="card-body">
                                                <div className="d-flex justify-content-between align-items-start mb-2">
                                                    <h5 className="card-title mb-0">IV Sodium Chloride 120ml/hr</h5>
                                                    <span className="badge bg-light text-secondary">Qty: 5</span>
                                                </div>
                                                <div className="d-flex justify-content-between align-items-center">
                                                    <div className="text-muted">
                                                        09/22/24 12:35 PM
                                                    </div>
                                                    <div className="action-icons">
                                                        <FontAwesomeIcon icon={faEdit} className="text-warning me-2 cursor-pointer" />
                                                        <FontAwesomeIcon icon={faTrashAlt} className="text-danger cursor-pointer" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="code-scan-btn">

                <div id="reader" style={{ width: "300px", margin: "auto", paddingTop: "20px" }}></div>

                {scannedCode && <p><strong>Scanned Code:</strong> {scannedCode}</p>}
                {error && <p style={{ color: "red" }}>{error}</p>}

                <button className="btn custom-bg-blue text-white rounded-circle" onClick={startScanner} disabled={scannerRunning}>
                    <FontAwesomeIcon icon={faQrcode} />
                </button>
            </div>

        </div>
    );
};
export default Patient;