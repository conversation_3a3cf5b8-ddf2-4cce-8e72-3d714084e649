import {React,useEffect,useState} from "react";
import moment from 'moment';

const Footer=()=> {
  const [year, setYear] = useState('');
   useEffect(() => {
    setYear(moment().format('yyyy'));
  }, []);
  return (
    <footer className="pt-2 mt-3 border-top border-color-primary bg-white shadow text-muted text-center text-small">
        <p className="mb-1">&copy; {year} Prime Healthcare</p>
        <ul className="list-inline">
            <li className="list-inline-item color-orange-imp">Privacy</li>
            <li className="list-inline-item color-orange-imp">Terms</li>
            <li className="list-inline-item color-orange-imp">Support</li>
        </ul>
    </footer>
  );
}
export default Footer;