.unit-list {
    flex-grow: 1;
    overflow-y: auto;
    padding: 1rem 0.5rem;
    height: calc(100vh - 375px);
    background-color: #f9f9f9;
}

.unit {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #eee;
    border-radius: 8px;
    background-color: #fff;
    height: 100%;
    transition: all 0.2s ease;
    cursor: pointer;
    gap: 0.75rem;
}

.form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    /* margin: 0; */
    cursor: pointer;
    border: 2px solid #165a9e;
}

.form-check-input:checked {
    background-color: #165a9e;
    border-color: #165a9e;
}

.unit-name {
    font-size: 1rem;
    margin-bottom: 0;
    flex-grow: 1;
}

.unit-icon {
    color: #165a9e;
    font-size: 1.2rem;
}

.continue {
    padding: 1rem;
    background-color: #fff;
    border-top: 1px solid #eee;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    width: 100%;
}

.continue button {
    width: 10%;
    background-color: #165a9e;
    color: white;
    border: none;
    padding: 12px;
    font-size: 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.continue button:hover {
    background-color: #0f4071;
    color: #ffffff80;
}

@media (min-width: 992px) {
    .unit:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }
}

@media (max-width: 1024px) {
    .continue button {
        width: 20%;
    }
}

@media (max-width: 768px) {
    .unit-list {
        padding: 0.5rem;
    }

    .unit {
        padding: 0.5rem;
        gap: 0.5rem;
    }

    .form-check-input {
        width: 1.1rem;
        height: 1.1rem;
    }

    .unit-name {
        font-size: 0.9rem;
    }

    .unit-icon {
        font-size: 1rem;
    }

    .continue button {
        font-size: 0.9rem;
        width: 40%;
    }
}

@media (max-width: 576px) {
    .unit-list {
        padding: 0.5rem;
        background-color: #fff;
    }

    .unit {
        padding: 0.5rem;
        border: none;
        border-radius: 0;
        background-color: transparent;
        border-bottom: 1px solid #ddd;
    }

    .continue button {
        width: 100%;
    }
}