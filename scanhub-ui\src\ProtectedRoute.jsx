import { Outlet, useNavigate } from 'react-router-dom';
import React, { useEffect } from 'react';

const ProtectedRoute = () => {
    let navigate = useNavigate();
    const token = localStorage.getItem('account.token');
    useEffect(() => {
        if (!token) {
            navigate('/');
        }else{
            navigate('/facilities')
        }
    }, [token]);
    return (
        <Outlet />
    )
}

export default ProtectedRoute;