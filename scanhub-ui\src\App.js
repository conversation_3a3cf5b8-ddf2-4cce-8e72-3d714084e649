import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { React } from "react";
import Layout from './Layout';
import Login from './Login';
import Forbidden from './Forbidden';
import Facilities from './components/Facilities';
import Units from './components/Units';
import Patients from './components/Patients';
import Patient from './components/Patient';
import ProtectedRoute from './ProtectedRoute';
import $ from 'jquery';

function App() {
  $('body').addClass('d-flex flex-column h-100');
  return (
    <Router>
      <Routes>
        <Route element={<ProtectedRoute />}>
        <Route path="/" element={<Login />} />
        <Route path="/facilities" element={<Layout><Facilities /></Layout>} />
        <Route path="/units" element={<Layout><Units /></Layout>} />
        <Route path="/patients" element={<Layout><Patients /></Layout>} />
        <Route path="/patient" element={<Layout><Patient /></Layout>} />
        </Route>
        <Route path="/forbidden" element={<Layout><Forbidden /></Layout>} />
        <Route path="*" element={<Layout><Forbidden /></Layout>} />
      </Routes>
    </Router>
  );
}

export default App;
