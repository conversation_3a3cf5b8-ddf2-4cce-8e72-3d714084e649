import CryptoJS from 'crypto-js';

// Encrypt function
export const dataEnc = (credencial,data) => {
    const enc_key = CryptoJS.enc.Utf8.parse(getParts(credencial.app_id, '$', '#')); // Replace with your 32-byte key
    const enc_iv = CryptoJS.enc.Utf8.parse(getParts(credencial.auto_id, '&', '*')); // Replace with your 16-byte IV
    const encrypted = CryptoJS.AES.encrypt(CryptoJS.enc.Utf8.parse(data), enc_key, {
        iv: enc_iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
    });
    return encrypted.toString();
};

// Decrypt function
export const dataDec = (credencial,ciphertext) => {
  const enc_key = CryptoJS.enc.Utf8.parse(getParts(credencial.app_id, '$', '#')); // Replace with your 32-byte key
  const enc_iv = CryptoJS.enc.Utf8.parse(getParts(credencial.auto_id, '&', '*')); // Replace with your 16-byte IV
    const decrypted = CryptoJS.AES.decrypt(ciphertext, enc_key, {
        iv: enc_iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
    });
    return decrypted.toString(CryptoJS.enc.Utf8);
};

const getParts = (sentence, first, last) => {
    let goodParts = [];
    const allParts = sentence.split(first);

    allParts.forEach((part) => {
        if (part.indexOf(last) > -1) {
            const goodOne = (part.split(last))[0];
            goodParts = goodParts.concat(goodOne);
        }
    });

    let stringParts = goodParts;
    return stringParts.join('');
};