import { Injectable } from '@angular/core';
import * as Forge from 'node-forge';

@Injectable({
  providedIn: 'root',
})
export class RSAHelper {
  publicKey: string = `-----BEGIN PUBLIC KEY-----
  MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCEsYU1ZkgHOhP6hnDHUhMW4MN2
  LLoLbMBJMaG5briODgJkZ69w17xbmCl5TiB+wEGInsb+lueLik6sVaagJuClE0JN
  tTtXnBTigJ3ghom59AYLXFlY4fKpuHxNGYtz7weSFDCr0XNYZUlZUP2UT1qi4tBO
  jpsSawHpsPtOCtrGoQIDAQAB
  -----END PUBLIC KEY-----`;

  constructor() {}

  encryptWithPublicKey(valueToEncrypt: string): string {
    const rsa = Forge.pki.publicKeyFromPem(this.publicKey);
    return window.btoa(rsa.encrypt(valueToEncrypt.toString()));
  }
}