import React, { useRef, useState } from 'react';
import { useIdleTimer } from 'react-idle-timer';
import { Modal } from 'react-bootstrap';
import { logout, signOut } from './services/authentication.service';
import { useNavigate } from 'react-router-dom';

const IdleLogout = () => {
    const [showModal, setShowModal] = useState(false);
    const [remaining, setRemaining] = useState(10); //
    const timerRef = useRef(null);
    let navigate = useNavigate();
    const handleClose = () => setShowModal(false);

    const onIdle = () => {
        console.log('ideal started!');
        setShowModal(true);
        startCountdown();
    };

    const onActive = () => {
        clearTimeout(timerRef.current);
        setShowModal(false);
        setRemaining(10);
    };

    const startCountdown = () => {
        timerRef.current = setInterval(() => {
            setRemaining(prev => {
                if (prev <= 1) {
                    clearInterval(timerRef.current);
                    handleLogout();
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);
    };

    const handleLogout = () => {
        // Perform logout logic here
        setShowModal(false);
        appLogout();
    };

    const appLogout = () => {
        try {
            signOut().then((resp) => {
            }).catch((error) => {
                console.log('Failed to logout: ' + error);
            });
        } catch (er) {
            console.error('Error loging out: ', er);
        } finally {
            logout();
            navigate('/');
        }
    };

    useIdleTimer({
        timeout: 1000 * 60 * 10, // 10 minutes
        onIdle,
        onActive,
        debounce: 500
    });


    return (
        <Modal show={showModal} onHide={handleClose}>
            <Modal.Header closeButton>
                <Modal.Title>Idle Alert</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <h3>You've been idle</h3>
                <p>You will be logged out in {remaining} seconds.</p>
            </Modal.Body>
            <Modal.Footer>
                <button type="submit" className="btn btn-primary" onClick={onActive}>Stay Logged In</button>
            </Modal.Footer>
        </Modal>
    );

};
export default IdleLogout;
