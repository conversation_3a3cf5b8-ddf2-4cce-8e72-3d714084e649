{"name": "scanhub-ui", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.9.0", "bootstrap": "^5.3.6", "html5-qrcode": "^2.3.8", "jquery": "^3.7.1", "moment": "^2.30.1", "react": "^19.1.0", "react-bootstrap": "^2.10.9", "react-device-detect": "^2.2.3", "react-dom": "^19.1.0", "react-hook-form": "^7.56.3", "react-idle-timer": "^5.7.2", "react-router-dom": "^7.5.3", "react-scripts": "5.0.1", "sweetalert2": "^11.21.2", "sweetalert2-react-content": "^5.1.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}