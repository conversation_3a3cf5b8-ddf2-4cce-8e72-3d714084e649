import apiClient from '../index';

export const logout = () => {
    localStorage.removeItem('account.token');
    localStorage.removeItem('account.auth');
    localStorage.removeItem('account.access');
}

export const getUserAccess = (s_id) => {
    return apiClient.post(`/api/Common/GetUserAccess`,null,{ headers: {'SHSessionId':s_id}});
};

export const signIn = (data) => {
    return apiClient.post(`/api/Authentication/SignIn`,data);
};

export const signOut = () => {
    return apiClient.post(`/api/Authentication/SignOut`,null);
};