.header {
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
}

.header .logo {
  display: flex;
  align-items: center;
}

.header .logo span {
  font-weight: bold;
}

.header .logo strong {
  padding: 0 6px;
  border-radius: 4px;
  font-weight: 600;
}

.header .logo img {
  width: 25px;
  height: 25px;
  margin-right: 10px;
}

.header-flex {
  width: 100%;
  display: flex;
  align-items: center;
}

/* Guaranteed right alignment */
.navbar-toggler-right {
  margin-left: auto !important;
  display: block !important;
}

/* iOS touch target size */
@supports (-webkit-touch-callout: none) {
  .navbar-toggler {
    min-width: 44px;
    min-height: 44px;
    display: flex !important;
    align-items: center;
    justify-content: center;
  }
}

/* Remove any conflicting Bootstrap styles */
.navbar-toggler {
  border: none !important;
  padding: 10px !important;
  margin: 0 !important;
}

/* iOS Safari specific sticky header */
@supports (-webkit-touch-callout: none) {
  .navbar.fixed-top {
    position: -webkit-sticky;
    position: sticky;
  }
}