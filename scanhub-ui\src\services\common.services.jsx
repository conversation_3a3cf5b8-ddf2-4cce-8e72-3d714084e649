import apiClient from '../index';

export const getFacilitiesByUser = () => {
    return apiClient.get(`/api/Common/GetAllFacilities`);
};

export const updateFacilityAsFavorite = (request) => {
    return apiClient.post(`/api/Common/UpdateFacilityAsFavorite`,request);
};

export const getAllUnits = (request) => {
    return apiClient.post(`/api/Common/GetAllUnits`,request);
};

export const updateUnitAsFavorite = (request) => {
    return apiClient.post(`/api/Common/UpdateUnitAsFavorite`,request);
};