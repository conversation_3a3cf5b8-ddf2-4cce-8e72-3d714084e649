.facility-list {
  flex-grow: 1;
  overflow-y: auto;
  padding: 1rem;
  max-height: calc(100vh - 200px);
  min-height: 400px;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  scroll-behavior: smooth;
  position: relative;
  z-index: 1;
}

.facility {
  cursor: pointer;
}

.facility img {
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 5px;
}

.pin {
  font-size: 1.2rem;
  padding-left: 1rem;
  display: inline-block;
  width: 40px;
  height: 24px;
  position: relative;
  text-align: center;
}

.pin svg {
  transition: all 0.2s ease-in-out;
}

.facility-details h4 {
  font-weight: 600;
}

/* Ensure the main container allows scrolling */
.desktop-container {
  overflow: visible;
}

/* Ensure tab content is scrollable */
.tab-content {
  overflow: visible;
}

.tab-pane {
  overflow: visible;
}


@media (max-width: 768px) {
  .facility-list {
      padding: 0.5rem;
      max-height: calc(100vh - 180px);
      min-height: 300px;
  }
}

@media (max-width: 576px) {
  .facility-list {
      padding: 0.5rem;
      max-height: calc(100vh - 160px);
      min-height: 250px;
  }
}

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
  .facility-list {
    /* Force hardware acceleration */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    /* Better iOS scrolling */
    -webkit-overflow-scrolling: touch;
    /* Fix for iOS Safari viewport height issues using custom property */
    max-height: calc(var(--vh, 1vh) * 100 - 200px);
    min-height: calc(var(--vh, 1vh) * 100 - 400px);
    /* Fallback for browsers that don't support custom properties */
    max-height: calc(100vh - 200px);
    min-height: calc(100vh - 400px);
    /* Ensure proper positioning */
    position: relative;
    z-index: 1;
  }

  /* iOS Safari mobile fixes */
  @media (max-width: 768px) {
    .facility-list {
      max-height: calc(var(--vh, 1vh) * 100 - 180px);
      min-height: calc(var(--vh, 1vh) * 100 - 350px);
      /* Fallback */
      max-height: calc(100vh - 180px);
      min-height: calc(100vh - 350px);
    }
  }

  @media (max-width: 576px) {
    .facility-list {
      max-height: calc(var(--vh, 1vh) * 100 - 160px);
      min-height: calc(var(--vh, 1vh) * 100 - 300px);
      /* Fallback */
      max-height: calc(100vh - 160px);
      min-height: calc(100vh - 300px);
    }
  }

  /* Fix for iOS Safari with dynamic viewport */
  .desktop-container {
    min-height: -webkit-fill-available;
    min-height: calc(var(--vh, 1vh) * 100);
    position: relative;
    overflow: visible;
  }

  /* Ensure tab content works properly on iOS */
  .tab-content {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    position: relative;
  }

  .tab-pane {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
}

/* SweetAlert2 mobile fixes */
.swal-mobile-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 9999 !important;
}

/* iOS Safari specific SweetAlert2 fixes */
@supports (-webkit-touch-callout: none) {
  .swal2-container {
    position: fixed !important;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  .swal2-popup {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    position: relative !important;
  }

  /* Prevent body scroll when modal is open */
  body.swal2-shown {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
  }

  /* Ensure modal backdrop doesn't interfere with scrolling */
  .swal2-backdrop-show {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
}