import React,{useState,useEffect} from 'react';
import './Login.css';
import logo from './assets/PrimeIcon.jpeg';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEyeSlash } from '@fortawesome/free-solid-svg-icons';
import { faArrowRight } from '@fortawesome/free-solid-svg-icons';
import { useForm } from 'react-hook-form';
import { signIn } from './services/authentication.service';
import { useNavigate } from 'react-router-dom';
import backgroundImage from './assets/abstract-watercolor.png';

const Login = () => {
    const { register, handleSubmit,reset, formState: { errors } } = useForm();
    let navigate = useNavigate();
    const [isLoading, setIsLoading] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    useEffect(() => {
        document.body.style.backgroundImage = `url(${backgroundImage})`;
        // Optional cleanup
        return () => {
            document.body.style.backgroundImage = '';
        };
    }, []);
    
    const onSubmit = (data) => {
        setErrorMessage('');
        let request = {
            EmailAddress: data.EmailAddress,
            PasswordBase64: btoa(data.PasswordBase64),
            LoginFlag: true
        };
        try {
            startSpinner();
            signIn(request).then((resp) => {
                stopSpinner();
                if (resp.data.Jwt) {
                    reset();
                    localStorage.setItem('account.token', JSON.stringify(resp.data.Jwt.Token));
                    navigate('/facilities');
                } else {
                    setErrorMessage('invalid credentials');
                }

            }).catch((error) => {
                const status = error.response ? error.response.status : null;
                if (status === 403) { navigate('/forbidden'); }
                console.log('Failed to login: ' + error);
                stopSpinner();
            });
        } catch (er) {
            console.error('Error loging in: ', er);
        }
    };
    
    const startSpinner = async () => {
        setIsLoading(true);
    };
    const stopSpinner = async () => {
        setIsLoading(false);
    };

    return (
            <div className="row justify-content-center">
                {isLoading&&<div className="loader-wrapper"><div className="loader"></div></div>}
                <div className="col-lg-5 col-md-7 col-sm-9">
                    <div className="login-header mb-md-1 mb-lg-3">
                        <div className="logo mb-1">
                            <img src={logo} alt="logo" className="me-1" /> Prime Healthcare
                        </div>
                        <h2 className="mb-0"><span className="logo-scan custom-color-maroon">Scan</span><span className="logo-hub custom-bg-blue">hub</span></h2>
                    </div>
                    <div className="login-container">
                        <div className="login-body">
                            <h4 className="info h4 fw-bold">Login</h4>
                            <p className="sub-info text-muted mb-4">Enter your credentials to continue</p>
                            <form onSubmit={handleSubmit(onSubmit)} id="login-from">
                                <div className="mb-3">
                                <input id="EmailAddress"
                                    onKeyDown={(e) => {
                                        if (e.key === 'Enter') {
                                            e.preventDefault();
                                            handleSubmit(onSubmit)();
                                        }
                                    }}
                                    type="text" name="EmailAddress" {...register('EmailAddress', { required: true })}
                                    className="form-control" placeholder="Username" />
                                    {errors.EmailAddress && <div className='text-danger'>Username is required.</div>}
                                </div>

                                <div className="mb-4">
                                    <div className="input-group position-relative">
                                    <input id="PasswordBase64"
                                        onKeyDown={(e) => {
                                            if (e.key === 'Enter') {
                                                e.preventDefault();
                                                handleSubmit(onSubmit)();
                                            }
                                        }} type="password" name="PasswordBase64" {...register('PasswordBase64', { required: true })} className="form-control" placeholder="Password" />
                                        <span className="password-toggle">
                                            <FontAwesomeIcon icon={faEyeSlash} />
                                        </span>
                                    </div>
                                    {errors.PasswordBase64 && <div className='text-danger'>Password is required.</div>}
                                </div>
                                <div className="mb-4">{errorMessage && <div className='text-danger'>{errorMessage}.</div>}</div>

                                <button type="submit" className="btn btn-continue custom-bg-blue w-100">
                                    Continue <FontAwesomeIcon className='ms-2' icon={faArrowRight} />
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
    );
}
export default Login;