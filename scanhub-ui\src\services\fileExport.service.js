import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { saveAs } from 'file-saver';

export const  exportToExcel = async (data, file_name)  => {
    const Excel = require('exceljs');
    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet("My Sheet");
    worksheet.columns = [
        { header: 'Incident Id', key: 'incident_id'},
        { header: 'Date of Incident', key: 'date_of_incident'},
        { header: 'Time of Incident', key: 'time_of_incident'},
        { header: 'Facility Name', key: 'facility_name'},
        { header: 'Location Name', key: 'location_name'},
        { header: 'Sub Location Name', key: 'sub_location_name'},
        { header: 'Categories Name', key: 'categories_name'},
        { header: 'Incident Desc', key: 'incident_desc'},
        { header: 'People Involved', key: 'people_involved'},
        { header: 'Witnesses Involved', key: 'witnesses_involved'},
        { header: 'Notes Count', key: 'notes_count'},
        { header: 'Attachments Count', key: 'attachments_count'},
        { header: 'Created By', key: 'created_by'},
        { header: 'Status Name', key: 'status_name'},
        { header: 'Status Updated By', key: 'status_updated_by'},
        { header: 'Status Updated Date', key: 'status_updated_date'},
        { header: 'Latest Updated Date', key: 'latest_updated_date'},
    ];
    data.map((item) => {
        let n_count=item?.listOfNotes?.length?item?.listOfNotes?.length:item.notes_count;
        let d_count=item?.listOfDocs?.length?item?.listOfDocs?.length:item.attachments_count;
        
        let requiredItem={
        incident_id:item.incident_id,
        date_of_incident:item.date_of_incident,
        time_of_incident:item.time_of_incident,
        facility_name:item.facility_name,
        location_name:item.location_name,
        sub_location_name:item.sub_location_name,
        categories_name:item.categories_name,
        incident_desc:item.incident_desc,
        people_involved:item.people_involved,
        witnesses_involved:item.witnesses_involved,
        notes_count:n_count,
        attachments_count:d_count,
        created_by:item.created_by,
        status_name:item.status_name,
        status_updated_by:item.status_updated_by,
        status_updated_date: item.status_updated_date,
        latest_updated_date:item.latest_updated_date
        };

        worksheet.addRow(requiredItem);
    });
    // save under export.xlsx
    workbook.xlsx.writeBuffer().then(buffer => saveAs(new Blob([buffer]),file_name + '.xlsx'));
};

export const exportToPDF = (input, file_name) => {
    html2canvas(input).then((canvas) => {
        const imgData = canvas.toDataURL('image/png');
        const pdf = new jsPDF(
            {
                format: 'a4',
                unit: 'px',
                margin: 40
            });
        const imgProps = pdf.getImageProperties(imgData);
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

        pdf.addImage(imgData, 'PNG', 0, 20, pdfWidth, pdfHeight);

        pdf.save(file_name + '.pdf');
    });
};

export const printToPDF = (input) => {
    html2canvas(input).then((canvas) => {
        const imgData = canvas.toDataURL('image/png');
        const pdf = new jsPDF(
            {
                format: 'a4',
                unit: 'px',
                margin: 40
            });
        const imgProps = pdf.getImageProperties(imgData);
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

        pdf.addImage(imgData, 'PNG', 0, 20, pdfWidth, pdfHeight);

        const pdfBlob = pdf.output('blob');
        const pdfUrl = URL.createObjectURL(pdfBlob);
        const printWindow = window.open(pdfUrl);
        printWindow.onload = () => {
            printWindow.print();
        };
    });
};

