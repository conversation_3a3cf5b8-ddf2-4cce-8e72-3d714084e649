import { React, useEffect, useState, useContext, useRef } from "react";
import { useNavigate, useLocation, NavLink } from 'react-router-dom';
import './Patients.css';
import globalContext from '../GlobalContext';
import { getPatientsData } from '../services/patient.services';
import { faFilter, faArrowUpWideShort, faArrowDownWideShort } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import moment from 'moment';
import { logout } from '../services/authentication.service';
import { isMobile as detectMobile } from 'react-device-detect';

const Patients = () => {

    const location = useLocation();
    let navigate = useNavigate();
    const GlobalContext = useContext(globalContext);
    const containerRef = useRef(null); // Ref for scrolling
    const [facilityObj, setFacilityObj] = useState({});
    const [selectedUnits, setSelectedUnits] = useState([]);
    const [listOfPatients, setListOfPatients] = useState([]);
    const [isSorted, setIsSorted] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [searchTermValue, setSearchTermValue] = useState('');
    const rowsPerPage = 10;
    const [currentPageNo, setCurrentPageNo] = useState(1);
    const [totalCount, setTotalCount] = useState(0);
    const [isMobile, setIsMobile] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [loading, setLoading] = useState(false);
    const [isMyFaciliitesTabActive, setIsMyFaciliitesTabActive] = useState(true);
    const [isPinndedTabActive, setIsPinndedTabActive] = useState(true);
    const [totalPages, setTotalPages] = useState(1);

    // Initialize state
    useEffect(() => {
        setIsMobile(detectMobile);
        setFacilityObj(location.state.facilityObj);
        setIsMyFaciliitesTabActive(location.state.isMyFaciliitesTabActive);
        setIsPinndedTabActive(location.state.isPinndedTabActive);
        setSelectedUnits(location.state.selectedUnits);
        setSearchTerm(location.state.searchTerm);
        setSearchTermValue(location.state.searchTerm);
        setCurrentPageNo(location.state.currentPageNo);
        setIsSorted(location.state.isSorted);
        //fetchData(location.state.currentPageNo); // Initial load
    }, []);

    // Scroll listener for container
    useEffect(() => {
        const container = containerRef.current;
        if (!container) return;

        const handleScroll = () => {
            const { scrollTop, scrollHeight, clientHeight } = container;
            if (!loading && hasMore && scrollTop + clientHeight >= scrollHeight - 100) {
                setCurrentPageNo(prev => prev + 1);
            }
        };
        if (isMobile) {
            container.addEventListener('scroll', handleScroll, { passive: true });
            return () => container.removeEventListener('scroll', handleScroll);
        }
    }, [loading, hasMore]);

    useEffect(() => {
        fetchData(currentPageNo);
    }, [currentPageNo,searchTermValue]);

    const fetchData = async (pageNo) => {
        try {
            GlobalContext.startSpinner();
            setLoading(true);
            let units = location.state.selectedUnits.map(item => item.Unit_Id).join(',');
            let request = {
                Param1: searchTermValue,
                Param2: String(location.state.facilityObj.Facility_ID),
                Param3: String(units),
                Param4: String(pageNo),
                Param5: String(rowsPerPage)
            };

            const resp = await getPatientsData(request);
            const newPatients = resp.data?.Patients || [];
            const totalRecords = resp.data?.TotalCount || 0;
            setTotalCount(totalRecords);
            setHasMore(newPatients.length < totalRecords);
            const totlPages = Math.ceil(totalRecords / rowsPerPage);
            setTotalPages(totlPages);
            if (isMobile && currentPageNo>1) {
                setListOfPatients(prev => {
                    const updatedList = [...prev, ...newPatients];
                    return updatedList;
                });
            } else {
                setListOfPatients(newPatients);
            }

        } catch (error) {
            const status = error.response?.status;
            if (status === 403) navigate('/forbidden');
            if (status === 401) { alert('Session expired'); logout(); navigate('/'); }
            console.error('Failed to fetch data: ', error);
        } finally {
            GlobalContext.stopSpinner();
            setLoading(false);
        }

    };

    const navigateToPatient = (item) => {
        navigate('/patient', {
            state: { facilityObj: facilityObj, isMyFaciliitesTabActive: isMyFaciliitesTabActive, selectedUnits: selectedUnits, patientObj: item, currentPageNo: currentPageNo, searchTerm: searchTerm, isSorted: isSorted }
        });
    };

    const handleSearch = () => {
        setCurrentPageNo(1);
        setSearchTermValue(searchTerm);
    };

    const handleASortClick = () => {
        const sorted = [...listOfPatients].sort((a, b) => a.PatientName.localeCompare(b.PatientName));
        setListOfPatients(sorted);
        setIsSorted(true);
    };

    const handleDSortClick = () => {
        const sorted = [...listOfPatients].sort((a, b) => b.PatientName.localeCompare(a.PatientName));
        setListOfPatients(sorted);
        setIsSorted(false);
    };

    const back = () => {
        navigate('/units', {
            state: { facilityObj: facilityObj, isMyFaciliitesTabActive: isMyFaciliitesTabActive, isPinndedTabActive: isPinndedTabActive, selectedUnits: selectedUnits }
        });
    }

    return (
        <div className="col-lg-12 desktop-container p-0 d-flex flex-column">
            <a className="back-btn-link p-2 border-bottom fw-bold custom-color-maroon btn-link" onClick={back}>{facilityObj.Facility_Name}</a>
            <div className="text-left p-2 border-bottom fw-bold fs-5">Patients</div>
            <div className="search-box d-flex align-items-center gap-2 pt-3 px-3 py-1 custom-bg-light-gray ">
                <input type="text" className="form-control form-control-sm border-0"
                    value={searchTerm} onChange={e => setSearchTerm(e.target.value)} placeholder="Search by name/account#" />
                <div className="search-icon d-flex align-items-center gap-2">
                    <button className="btn btn-sm btn-outline-secondary border-0 bg-white" type="button" onClick={handleSearch}>
                        <FontAwesomeIcon icon={faFilter} />
                    </button>

                    {isSorted ?
                        <button className="btn btn-sm btn-outline-secondary border-0 bg-white" type="button" onClick={handleDSortClick}>
                            <FontAwesomeIcon icon={faArrowDownWideShort} />
                        </button>

                        :
                        <button className="btn btn-sm btn-outline-secondary border-0 bg-white" type="button" onClick={handleASortClick}>
                            <FontAwesomeIcon icon={faArrowUpWideShort} />
                        </button>

                    }
                </div>
            </div>

            <div ref={containerRef} className="patient-list p-3 flex-grow-1 custom-bg-light-gray">
                <div className="row g-3">

                    {listOfPatients.map((item, index) => (
                        <div className="col-lg-3 col-md-6 col-12 cursor-pointer" key={index} onClick={() => navigateToPatient(item)}>
                            <div className="patient-card card mb-3 p-2 border-0">
                                <div className="patient-name">{item.PatientName} | <span>{moment(item.Dob).format('MM/DD/YY')} ({item.Sex})</span></div>
                                <div className="patient-meta">{item.AccountNumber} &nbsp; {item.PatientLocation}</div>
                                <div className="patient-meta">{moment(item.AdmissionDate).format('MM/DD/YY')}</div>
                                <div className="patient-meta">Acute pancreatitis without necrosis or infection</div>
                            </div>
                        </div>

                    ))}
                </div>

                {(isMobile && loading) && <p className="text-center">Loading more patients...</p>}

                {(isMobile && !hasMore) && <p className="text-center">No more records to load.</p>}

                {(!isMobile && totalPages==0) && <p className="text-center">No patients found.</p>}

                {(!isMobile && totalPages>1) && <div className="row g-3 float-end">
                    <div>
                    <button
                        onClick={() => setCurrentPageNo(prev => Math.max(prev - 1, 1))}
                        disabled={currentPageNo === 1}> Previous </button>

                    <span> Page {currentPageNo} of {totalPages} </span>

                    <button
                        onClick={() => setCurrentPageNo(prev => Math.min(prev + 1, totalPages))}
                        disabled={currentPageNo === totalPages}> Next </button>
                    </div>
                </div>}

            </div>
        </div>
    );

};

export default Patients;